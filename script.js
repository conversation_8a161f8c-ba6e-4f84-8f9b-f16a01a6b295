// CrowdSnap - AI-Powered Event Management
// JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the application
    initializeApp();
});

function initializeApp() {
    // Header scroll effect
    initializeHeaderScroll();

    // Smooth scrolling for navigation links
    initializeSmoothScrolling();

    // Dashboard animations
    initializeDashboardAnimations();

    // App screen interactions
    initializeAppScreens();
}

// Header scroll effect
function initializeHeaderScroll() {
    const header = document.querySelector('header');

    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            header.style.background = 'rgba(46, 55, 63, 0.98)';
            header.style.backdropFilter = 'blur(15px)';
        } else {
            header.style.background = 'rgba(46, 55, 63, 0.95)';
            header.style.backdropFilter = 'blur(10px)';
        }
    });
}

// Smooth scrolling for navigation links
function initializeSmoothScrolling() {
    const navLinks = document.querySelectorAll('nav a[href^="#"]');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);

            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Dashboard animations
function initializeDashboardAnimations() {
    const dashboardCards = document.querySelectorAll('.dashboard-card');

    // Add hover effects to dashboard cards
    dashboardCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
            this.style.transition = 'transform 0.3s ease';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // Animate dashboard on load
    setTimeout(() => {
        const mainDashboard = document.querySelector('.main-dashboard');
        if (mainDashboard) {
            mainDashboard.style.animation = 'dashboardPulse 2s ease-in-out infinite alternate';
        }
    }, 1000);
}

// App screen interactions
function initializeAppScreens() {
    const appScreens = document.querySelectorAll('.app-screen');

    appScreens.forEach(screen => {
        // Add click interaction
        screen.addEventListener('click', function() {
            // Add a subtle click effect
            this.style.transform += ' scale(0.95)';
            setTimeout(() => {
                this.style.transform = this.style.transform.replace(' scale(0.95)', '');
            }, 150);
        });

        // Add random floating animation
        const randomDelay = Math.random() * 2000;
        setTimeout(() => {
            screen.style.animation = `floatScreen 4s ease-in-out infinite alternate`;
            screen.style.animationDelay = `${Math.random() * 2}s`;
        }, randomDelay);
    });
}

// Add CSS animations dynamically
function addDynamicStyles() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes dashboardPulse {
            0% { box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3); }
            100% { box-shadow: 0 30px 60px rgba(173, 217, 41, 0.2); }
        }

        @keyframes floatScreen {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-10px); }
        }
    `;
    document.head.appendChild(style);
}

// Initialize dynamic styles
addDynamicStyles();

// Utility function for future features
function showNotification(message, type = 'info') {
    console.log(`${type.toUpperCase()}: ${message}`);
    // This can be expanded to show actual notifications in the UI
}

// Export functions for potential future use
window.CrowdSnap = {
    showNotification,
    initializeApp
};