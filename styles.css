/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #2e373f;
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    background: rgba(46, 55, 63, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    padding: 15px 0;
    transition: all 0.3s ease;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 28px;
    font-weight: bold;
    background: linear-gradient(45deg, #add929, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 30px;
}

nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

nav a:hover {
    color: #add929;
}

nav a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, #add929, #ffffff);
    transition: width 0.3s ease;
}

nav a:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg,
        rgba(46, 55, 63, 0.9) 0%,
        rgba(0, 0, 0, 0.8) 50%,
        rgba(173, 217, 41, 0.2) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="gradient" cx="50%" cy="50%" r="50%"><stop offset="0%" style="stop-color:%23add929;stop-opacity:0.1"/><stop offset="100%" style="stop-color:%232e373f;stop-opacity:0.05"/></radialGradient></defs><circle cx="500" cy="500" r="500" fill="url(%23gradient)"/></svg>');
    background-size: cover;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="25" cy="25" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1.5" fill="rgba(173,217,41,0.2)"/><circle cx="50" cy="10" r="1" fill="rgba(255,255,255,0.15)"/></svg>');
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateY(0) rotate(0deg); }
    100% { transform: translateY(-100vh) rotate(360deg); }
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    z-index: 2;
    position: relative;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #add929 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.hero-text .subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    font-weight: 300;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.exhibition-visual {
    width: 600px;
    height: 450px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-gallery-container {
    position: relative;
    width: 100%;
    height: 100%;
    perspective: 1200px;
}

/* Main Dashboard Mockup */
.main-dashboard {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotateY(-8deg);
    width: 320px;
    height: 200px;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.4);
    z-index: 5;
    overflow: hidden;
}

.dashboard-header {
    height: 40px;
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    display: flex;
    align-items: center;
    padding: 0 15px;
    gap: 8px;
}

.dashboard-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.dot-1 { background: #ff5f57; }
.dot-2 { background: #ffbd2e; }
.dot-3 { background: #28ca42; }

.dashboard-title {
    color: white;
    font-size: 11px;
    font-weight: 600;
    margin-left: 10px;
}

.dashboard-content {
    padding: 20px 15px;
    height: calc(100% - 40px);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.dashboard-card {
    background: linear-gradient(135deg, #add929 0%, rgba(173, 217, 41, 0.1) 100%);
    border-radius: 8px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #2e373f;
    font-size: 10px;
    font-weight: 600;
    text-align: center;
}

.dashboard-card .icon {
    font-size: 18px;
    margin-bottom: 4px;
}

/* Floating App Screens */
.app-screen {
    position: absolute;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
    transition: all 0.4s ease;
}

.app-screen:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 30px 60px rgba(173, 217, 41, 0.3);
    border-color: rgba(173, 217, 41, 0.5);
}

.mobile-screen {
    width: 120px;
    height: 200px;
    top: 20px;
    right: 40px;
    transform: rotateY(15deg) rotateZ(-5deg);
    z-index: 4;
}

.tablet-screen {
    width: 180px;
    height: 130px;
    bottom: 40px;
    left: 20px;
    transform: rotateY(-12deg) rotateZ(3deg);
    z-index: 3;
}

.watch-screen {
    width: 80px;
    height: 80px;
    top: 60px;
    left: 60px;
    border-radius: 50%;
    transform: rotateY(20deg);
    z-index: 6;
}

/* Screen Content */
.screen-content {
    padding: 15px 10px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.mobile-screen .screen-content {
    padding: 20px 12px;
}

.screen-header {
    height: 25px;
    background: linear-gradient(135deg, #2e373f 0%, #add929 100%);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 8px;
    font-weight: 600;
    margin-bottom: 8px;
}

.screen-body {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px;
}

.mobile-screen .screen-body {
    grid-template-columns: 1fr;
    gap: 8px;
}

.screen-item {
    background: rgba(173, 217, 41, 0.1);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #2e373f;
    min-height: 20px;
}

.mobile-screen .screen-item {
    min-height: 25px;
    font-size: 10px;
}

/* Analytics Cards */
.analytics-card {
    position: absolute;
    background: rgba(46, 55, 63, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 12px;
    padding: 15px;
    color: white;
    border: 1px solid rgba(173, 217, 41, 0.3);
    transition: all 0.4s ease;
}

.analytics-card:hover {
    transform: translateY(-5px);
    border-color: #add929;
    box-shadow: 0 15px 30px rgba(173, 217, 41, 0.2);
}

.chart-card {
    width: 140px;
    height: 90px;
    top: 30px;
    right: 180px;
    transform: rotateY(-10deg);
    z-index: 2;
}

.stats-card {
    width: 120px;
    height: 70px;
    bottom: 80px;
    right: 200px;
    transform: rotateY(8deg);
    z-index: 2;
}

.card-title {
    font-size: 10px;
    color: #add929;
    margin-bottom: 8px;
    font-weight: 600;
}

.card-content {
    font-size: 8px;
    line-height: 1.3;
    opacity: 0.9;
}

.chart-visual {
    width: 100%;
    height: 40px;
    background: linear-gradient(135deg, rgba(173, 217, 41, 0.3) 0%, rgba(173, 217, 41, 0.1) 100%);
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.chart-visual::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60%;
    height: 70%;
    background: linear-gradient(135deg, #add929 0%, rgba(173, 217, 41, 0.7) 100%);
    border-radius: 2px 2px 0 0;
}

/* QR Code Element */
.qr-element {
    position: absolute;
    width: 60px;
    height: 60px;
    bottom: 180px;
    left: 180px;
    background: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    z-index: 7;
    border: 2px solid #add929;
}

/* Floating Particles */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(173, 217, 41, 0.6);
    border-radius: 50%;
}

.particle-1 { top: 10%; left: 20%; }
.particle-2 { top: 80%; right: 15%; }
.particle-3 { top: 40%; left: 10%; }
.particle-4 { bottom: 20%; right: 30%; }

/* CTA Buttons */
.cta-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, #add929 0%, #2e373f 100%);
    color: white;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Platform Section */
.platform-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 100px 0;
    position: relative;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 60px;
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.platform-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.platform-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #2e373f;
}

.platform-visual {
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    border-radius: 20px;
    padding: 40px;
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.platform-visual::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(173, 217, 41, 0.3), transparent);
    animation: rotate 10s linear infinite;
}

@keyframes rotate {
    100% { transform: rotate(360deg); }
}

/* Features Section */
.features-section {
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    padding: 100px 0;
    color: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.feature-card {
    background: linear-gradient(135deg,
        rgba(173, 217, 41, 0.1) 0%,
        rgba(46, 55, 63, 0.1) 100%);
    padding: 30px;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(173, 217, 41, 0.1), transparent);
    transition: left 0.5s ease;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: #add929;
}

.feature-card h3 {
    color: #add929;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

/* Gallery Section */
.gallery-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 100px 0;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 40px;
}

.gallery-item {
    aspect-ratio: 16/10;
    background: linear-gradient(135deg, #2e373f 0%, #add929 50%, #000000 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.gallery-item::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transition: all 0.5s ease;
    transform: translate(-50%, -50%);
}

.gallery-item:hover::before {
    width: 300px;
    height: 300px;
}

.gallery-item:hover {
    transform: scale(1.05);
}

/* Testimonials */
.testimonials-section {
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    padding: 100px 0;
    color: white;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.testimonial-card {
    background: linear-gradient(135deg,
        rgba(173, 217, 41, 0.05) 0%,
        rgba(46, 55, 63, 0.05) 100%);
    padding: 30px;
    border-radius: 15px;
    border-left: 4px solid #add929;
    backdrop-filter: blur(10px);
}

.testimonial-card h4 {
    color: #add929;
    margin-bottom: 10px;
}

/* Footer */
footer {
    background: linear-gradient(135deg, #000000 0%, #2e373f 100%);
    padding: 60px 0 30px;
    color: white;
    text-align: center;
}

.footer-cta {
    margin-bottom: 40px;
}

.footer-cta h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #add929 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
    margin-top: 30px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content,
    .platform-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .exhibition-visual {
        width: 450px;
        height: 350px;
    }

    .main-dashboard {
        width: 280px;
        height: 160px;
    }

    .mobile-screen {
        width: 100px;
        height: 160px;
        top: 15px;
        right: 30px;
    }

    .tablet-screen {
        width: 140px;
        height: 100px;
        bottom: 30px;
        left: 15px;
    }

    .watch-screen {
        width: 60px;
        height: 60px;
        top: 50px;
        left: 50px;
    }

    .chart-card {
        width: 120px;
        height: 75px;
        top: 25px;
        right: 140px;
    }

    .stats-card {
        width: 100px;
        height: 60px;
        bottom: 70px;
        right: 160px;
    }

    .qr-element {
        width: 50px;
        height: 50px;
        bottom: 140px;
        left: 140px;
        font-size: 24px;
    }

    nav ul {
        display: none;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }
}

/* Scroll animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}