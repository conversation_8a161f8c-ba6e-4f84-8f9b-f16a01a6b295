/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #2e373f;
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    background: rgba(46, 55, 63, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    padding: 15px 0;
    transition: all 0.3s ease;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 28px;
    font-weight: bold;
    background: linear-gradient(45deg, #add929, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 30px;
}

nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

nav a:hover {
    color: #add929;
}

nav a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, #add929, #ffffff);
    transition: width 0.3s ease;
}

nav a:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg,
        rgba(46, 55, 63, 0.9) 0%,
        rgba(0, 0, 0, 0.8) 50%,
        rgba(173, 217, 41, 0.2) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="gradient" cx="50%" cy="50%" r="50%"><stop offset="0%" style="stop-color:%23add929;stop-opacity:0.1"/><stop offset="100%" style="stop-color:%232e373f;stop-opacity:0.05"/></radialGradient></defs><circle cx="500" cy="500" r="500" fill="url(%23gradient)"/></svg>');
    background-size: cover;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="25" cy="25" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1.5" fill="rgba(173,217,41,0.2)"/><circle cx="50" cy="10" r="1" fill="rgba(255,255,255,0.15)"/></svg>');
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateY(0) rotate(0deg); }
    100% { transform: translateY(-100vh) rotate(360deg); }
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    z-index: 2;
    position: relative;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #add929 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.hero-text .subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    font-weight: 300;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.exhibition-visual {
    width: 600px;
    height: 450px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-gallery-container {
    position: relative;
    width: 100%;
    height: 100%;
    perspective: 1200px;
}

/* Main Dashboard Mockup */
.main-dashboard {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotateY(-8deg);
    width: 320px;
    height: 200px;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.4);
    z-index: 5;
    overflow: hidden;
}

.dashboard-header {
    height: 40px;
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    display: flex;
    align-items: center;
    padding: 0 15px;
    gap: 8px;
}

.dashboard-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.dot-1 { background: #ff5f57; }
.dot-2 { background: #ffbd2e; }
.dot-3 { background: #28ca42; }

.dashboard-title {
    color: white;
    font-size: 11px;
    font-weight: 600;
    margin-left: 10px;
}

.dashboard-content {
    padding: 20px 15px;
    height: calc(100% - 40px);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.dashboard-card {
    background: linear-gradient(135deg, #add929 0%, rgba(173, 217, 41, 0.1) 100%);
    border-radius: 8px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #2e373f;
    font-size: 10px;
    font-weight: 600;
    text-align: center;
}

.dashboard-card .icon {
    font-size: 18px;
    margin-bottom: 4px;
}

/* Floating App Screens */
.app-screen {
    position: absolute;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
    transition: all 0.4s ease;
}

.app-screen:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 30px 60px rgba(173, 217, 41, 0.3);
    border-color: rgba(173, 217, 41, 0.5);
}

.mobile-screen {
    width: 120px;
    height: 200px;
    top: 20px;
    right: 40px;
    transform: rotateY(15deg) rotateZ(-5deg);
    z-index: 4;
}

.tablet-screen {
    width: 180px;
    height: 130px;
    bottom: 40px;
    left: 20px;
    transform: rotateY(-12deg) rotateZ(3deg);
    z-index: 3;
}

.watch-screen {
    width: 80px;
    height: 80px;
    top: 60px;
    left: 60px;
    border-radius: 50%;
    transform: rotateY(20deg);
    z-index: 6;
}

/* Screen Content */
.screen-content {
    padding: 15px 10px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.mobile-screen .screen-content {
    padding: 20px 12px;
}

.screen-header {
    height: 25px;
    background: linear-gradient(135deg, #2e373f 0%, #add929 100%);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 8px;
    font-weight: 600;
    margin-bottom: 8px;
}

.screen-body {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px;
}

.mobile-screen .screen-body {
    grid-template-columns: 1fr;
    gap: 8px;
}